// 简历解析确认功能的类型定义

// 求职意向数据
export interface CareerIntentionData {
  workStatusId?: number; // 目前状态ID
  workingState?: string; // 目前状态
  expectWorkPlace?: string; // 期望工作地
  expectWorkPlaceIds?: number[]; // 期望工作地ID数组
  expectWorkPlaceName?: string[]; // 期望工作地名称数组
  expectSalary?: number; // 期望薪资
  expectSalaryVisible?: boolean; // 期望薪资是否面议
  expectCareer?: string; // 期望职能
  expectIndustry?: string; // 期望行业
  
  // 多个期望职位
  expectCareer1?: number; // 期望职位1 ID
  expectCareer1Name?: string; // 期望职位1 名称
  expectCareer2?: number; // 期望职位2 ID
  expectCareer2Name?: string; // 期望职位2 名称
  expectCareer3?: number; // 期望职位3 ID
  expectCareer3Name?: string; // 期望职位3 名称
  
  // 多个期望行业
  expectIndustry1?: number[]; // 期望行业1 ID数组
  expectIndustry1Names?: string[]; // 期望行业1 名称数组
  expectIndustry2?: number[]; // 期望行业2 ID数组
  expectIndustry2Names?: string[]; // 期望行业2 名称数组
  expectIndustry3?: number[]; // 期望行业3 ID数组
  expectIndustry3Names?: string[]; // 期望行业3 名称数组
  
  // 完善状态标识
  isExpectCareer1?: boolean; // 期望职位1是否待完善
  isExpectCareer2?: boolean; // 期望职位2是否待完善
  isExpectCareer3?: boolean; // 期望职位3是否待完善

  resumeId?: number; // 简历ID
}

// 工作经历数据
export interface WorkExperienceData {
  id?: number;
  resumeId?: number; // 简历ID
  positionType?: string; // 职位类型
  positionTypeId?: number; // 职位类型ID
  positionTypeName?: string; // 职位类型名称
  workIndustryName?: string; // 工作行业
  workIndustryId?: number; // 工作行业ID
  workIndustry?: number[]; // 工作行业数组（用于弹窗选择）
  experienceStartTime?: string; // 在职开始时间
  experienceFinishTime?: string; // 在职结束时间
  entName?: string; // 公司名称
  department?: string; // 部门
  workPlace?: string; // 工作地点
  positionDescription?: string; // 工作描述
  isToThisDay?: boolean; // 是否至今
}

// 教育经历数据
export interface EducationExperienceData {
  id?: number;
  resumeId?: number; // 简历ID
  school?: string; // 就读院校
  experienceStartTime?: string; // 在校时间
  experienceFinishTime?: string; // 在校时间
  education?: string; // 学历
  degreeId?: number; // 学历ID
  specialityInputName?: string; // 专业类别
  specialityId?: number; // 专业ID
  specialityName?: string; // 专业名称
  specialityDescription?: string; // 专业描述
}

// 项目经历数据
export interface ProjectExperienceData {
  id?: number;
  projectName?: string; // 项目名称
  beginTime?: string; // 项目时间
  endTime?: string; // 项目时间
  playRoleStr?: string; // 参与身份
  playRole?: number; // 项目ID
  projectDescription?: string; // 项目描述
  resumeId?: number; // 简历ID
}

// 培训经历数据
export interface TrainingExperienceData {
  trainId?: number;
  trainCourse?: string; // 培训课程
  trainInstitution?: string; // 培训机构
  trainBeginTime?: string; // 培训时间
  trainEndTime?: string; // 培训时间
  trainingDescription?: string; // 培训内容
  resumeId?: number; // 简历ID
}

// 技术能力数据
export interface TechnicalSkillsData {
  techId?: number;
  techName?: string; // 技能名称
  monthUsed?: string; // 使用时间
  techLevel?: string; // 掌握程度
  techLevelId?: number; // 掌握程度ID
  resumeId?: number; // 简历ID
}

// 语言技能数据
export interface LanguageSkillsData {
  id?: number;
  languageId?: number;// 外语语种ID
  languageName?: string; // 外语语种
  resumeId?: number; // 简历ID
  level?: number; // 综合能力
  levelName?: string; // 综合能力
  readLevel?: number; // 听说能力id
  readLevelName?: string; // 听说能力
  writeLevel?: number; // 读写能力id
  writeLevelName?: string; // 读写能力
}

// 证书职称数据
export interface CertificateData {
  certId?: number;
  certName?: string; // 证书名称
  certTypeTitle?: string; // 证书类型
  certificateType?: number; // 证书类型
  getTime?: string; // 获得时间
  resumeId?: number; // 简历ID
  levelVisible?: boolean; // 是否显示等级
  certTypeLevel?: number; // 证书等级
  certTypeLevelName?: string; // 证书等级名称
}

// 个人描述数据
export interface PersonalDescriptionData {
  desId?: number; // 描述ID
  resumeId?: number; // 简历ID
  description?: string; // 个人描述
}

// 完整的简历数据
export interface ResumeData {
  careerIntention?: CareerIntentionData;
  workExperiences?: WorkExperienceData[];
  educationExperiences?: EducationExperienceData[];
  projectExperiences?: ProjectExperienceData[];
  trainingExperiences?: TrainingExperienceData[];
  technicalSkills?: TechnicalSkillsData[];
  languageSkills?: LanguageSkillsData[];
  certificates?: CertificateData[];
  personalDescription?: PersonalDescriptionData[];
}

// 确认步骤枚举
export enum ConfirmationStep {
  CAREER_INTENTION = 0,
  WORK_EXPERIENCE = 1,
  EDUCATION_EXPERIENCE = 2,
  PROJECT_EXPERIENCE = 3,
  TRAINING_EXPERIENCE = 4,
  TECHNICAL_SKILLS = 5,
  LANGUAGE_SKILLS = 6,
  CERTIFICATES = 7,
  PERSONAL_DESCRIPTION = 8
}

// 步骤配置
export interface StepConfig {
  step: ConfirmationStep;
  title: string;
  description: string;
  hasMultipleRecords: boolean;
}

// 确认状态
export interface ConfirmationState {
  currentStep: ConfirmationStep;
  currentRecordIndex: number;
  totalSteps: number;
  totalRecords: number;
  confirmedSteps: Set<ConfirmationStep>;
  skippedSteps: Set<ConfirmationStep>;
}

// 步骤操作类型
export enum StepAction {
  CONFIRM = 'confirm',
  SKIP = 'skip',
  EDIT = 'edit'
}

// 步骤操作事件
export interface StepActionEvent {
  action: StepAction;
  step: ConfirmationStep;
  recordIndex?: number;
  data?: any;
}

// 字段验证结果
export interface FieldValidationResult {
  hasData: boolean;
  step: ConfirmationStep;
  fieldName: string;
}

// 动态步骤配置
export interface DynamicStepConfig extends StepConfig {
  isValid: boolean;
  dataCount: number;
}
