<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 就读院校 -->
      <el-form-item label="就读院校" prop="school">
        <el-select
          v-model="formData.school"
          class="field-input"
          filterable
          placeholder="请输入院校名称"
          allow-create
          :remote-method="collegeNameText"
          :loading="loadingSEl"
          remote
        >
          <el-option
            v-for="(item, index) in collegeList"
            :key="index"
            :label="item"
            :value="item"
            
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 在校时间 -->
      <el-form-item label="在校时间" required>
        <div style="display: flex; align-items: center">
          <div class="custom-date-picker">
            <el-form-item prop="experienceStartTime">
              <el-date-picker
                v-model="formData.experienceStartTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div class="line" style="margin: 0 10px">至</div>
          <div class="custom-date-picker">
            <el-form-item prop="experienceFinishTime">
              <el-date-picker
                v-model="formData.experienceFinishTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
        </div>
      </el-form-item>

      <!-- 学历 -->
      <el-form-item label="学历" prop="education">
        <el-select
          v-model="formData.education"
          placeholder="请选择学历"
          class="field-input"
        >
          <el-option
            v-for="(p, index) in educationalList"
            :key="index"
            :label="p.keywordName"
            :value="p.keywordID"
            @click="formData.degreeId = p.keywordID"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 专业类别 -->
      <el-form-item label="专业类别" prop="specialityName" v-if="iscollege">
        <el-input
          v-model="formData.specialityName"
          placeholder="请选择"
          class="w400"
          @click="showspeciality"
          readonly
        ></el-input>
      </el-form-item>

      <!-- 专业名称 -->
      <el-form-item
        label="专业名称"
        prop="specialityInputName"
        v-if="iscollege"
      >
        <el-input
          v-model="formData.specialityInputName"
          placeholder="请输入专业名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 专业描述 -->
      <!-- <el-form-item
        label="专业描述"
        prop="specialityDescription"
        v-if="iscollege"
      >
        <el-input
          v-model="formData.specialityDescription"
          type="textarea"
          :rows="3"
          :maxlength="1000"
          show-word-limit
          placeholder="请描述您的专业相关信息"
          class="field-input"
        />
      </el-form-item> -->
      <seleSpecialty
        @confirmSpeciality="confirmSpeciality"
        v-if="dialogVisible"
        :hideValue="formData.specialityId"
      ></seleSpecialty>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  onMounted,
  toRefs,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { EducationExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { getDegreeoptions } from "../../../http/dictionary";
import { useSaveStep } from "../composables/useSaveStep";
import type { ResumeEditEducationDto } from "../../../http/app/data-contracts";
import { useStore } from "vuex";
import seleSpecialty from "@/components/seleSpecialty.vue";
import { searchcollege } from "../../../http/searchAPI";
export default defineComponent({
  name: "EducationExperienceStep",
  components: {
    BaseConfirmationStep,
    seleSpecialty,
  },
  props: {
    data: {
      type: Object as () => EducationExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    const store = useStore();
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();
    const state = reactive({
      educationalList: [],
      dialogVisible: false,
      iscollege: true,
      loadingSEl: false,
      collegeList: [],
    });

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<EducationExperienceData>(
      {} as EducationExperienceData
    );

    // 表单验证规则
    const formRules = computed(() => ({
      school: [
        { required: true, message: "请输入就读院校", trigger: "blur" },
        { max: 40, message: "院校名称不能超过40个字符", trigger: "blur" },
      ],
      education: [{ required: true, message: "请选择学历", trigger: "change" }],
      specialityName: [
        { required: true, message: "请选择专业类别", trigger: "change" },
      ],
      specialityDescription: [
        { max: 1000, message: "专业描述不能超过1000个字符", trigger: "blur" },
      ],
      experienceFinishTime: [
        {
          type: "date",
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
      experienceStartTime: [
        {
          type: "date",
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          Object.assign(formData, newData);

          console.log("EducationExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );
    watch(
      () => formData.degreeId,
      (newValue, oldValue) => {
        state.iscollege = (newValue || 0) > 353 ? true : false;
      }
    );

    // 数据转换函数：将 EducationExperienceData 转换为 ResumeEditEducationDto
    // const transformDataForAPI = (data: EducationExperienceData): ResumeEditEducationDto => {
    //   const { startTime, endTime } = parseStudyTime(data.studyTime || '');

    //   return {
    //     id: data.id,
    //     resumeId: undefined, // 由API调用时设置
    //     school: data.schoolName || '',
    //     experienceStartTime: startTime,
    //     experienceFinishTime: endTime,
    //     degreeId: data.education ? educationNameToId(data.education) : undefined,
    //     fullTimeFlag: true, // 默认为全日制
    //     specialityId: undefined, // 如果有专业类别选择功能，可以在这里设置
    //     specialityInputName: data.majorName || undefined,
    //     specialityDescription: data.majorDescription || undefined
    //   };
    // };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      await executeSave(
        formData,
        ResumepartClass.apiResumepartSaveeducationPost,
        baseStepRef,
        emit,
        {
          // transformData: transformDataForAPI,
          onSaveSuccess: (result) => {
            console.log("教育经历保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("教育经历保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };

    const getEducationalList = async () => {
      let result = store.state.educationalList;
      if (result.length <= 0) {
        let res: any = await getDegreeoptions("");
        state.educationalList = res.data;
        store.commit("setEducationalList", res.data);
      } else {
        state.educationalList = result;
      }
    };
    const methods = {
      showspeciality() {
        state.dialogVisible = true;
      },
      //接收子集传来的数据---专业类别选择
      confirmSpeciality(data: any) {
        state.dialogVisible = false;
        if (data) {
          formData.specialityName = data.name;
          formData.specialityId = data.id;
          if (!formData.specialityInputName) {
            formData.specialityInputName = data.name;
          }
        }
      },
      collegeNameText(query: string) {
        if (query) {
          state.loadingSEl = true;
          setTimeout(() => {
            methods.getCollegeNameText(query);
            state.loadingSEl = false;
          }, 200);
        } else {
        }
      },
      // 就读学校--搜索
      async getCollegeNameText(text: any) {
        let data: any = await searchcollege(text);
        if (data.code == 1 && data.data.count > 0) {
          state.collegeList = data.data.result;
        }
      },
    };
    onMounted(async () => {
      await getEducationalList();
    });
    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      ...toRefs(state),
      ...methods,
    };
  },
});
</script>
<style lang="less">
.custom-date-picker {
  width: 190px;
  .w160 {
    width: 190px !important;
    .el-input__inner {
      width: 190px !important;
    }
  }
}
</style>
