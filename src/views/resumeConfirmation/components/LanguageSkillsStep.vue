<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 外语语种 -->
      <el-form-item label="外语语种">
        <el-select
          v-model="formData.languageName"
          placeholder="请选择外语语种"
          class="field-input"
        >
          <el-option
            v-for="item in languageList"
            :key="item.keywordID"
            :label="item.keywordName"
            :value="item.keywordID"
            @click="selectlanguageName(item.keywordID)"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 综合能力 -->
      <el-form-item label="综合能力">
        <el-select
          v-model="formData.levelName"
          placeholder="请选择综合能力水平"
          class="field-input"
        >
          <el-option
            v-for="item in mixedAbility"
            :key="item.keywordID"
            :label="item.keywordName"
            :value="item.keywordID"
            @click="formData.level = item.keywordID"
          />
        </el-select>
      </el-form-item>

      <!-- 听说能力 -->
      <el-form-item label="听说能力">
        <el-select
          v-model="formData.readLevelName"
          placeholder="请选择听说能力水平"
          class="field-input"
        >
          <el-option
            v-for="item in columns"
            :key="item.keywordID"
            :label="item.keywordName"
            :value="item.keywordID"
            @click="formData.readLevel = item.keywordID"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 读写能力 -->
      <el-form-item label="读写能力">
        <el-select
          v-model="formData.writeLevelName"
          placeholder="请选择读写能力水平"
          class="field-input"
        >
          <el-option
            v-for="item in columns"
            :key="item.keywordID"
            :label="item.keywordName"
            :value="item.keywordID"
            @click="formData.writeLevel = item.keywordID"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  onBeforeMount,
  toRefs,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { LanguageSkillsData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { useSaveStep } from "../composables/useSaveStep";
import {
  getLanguageoptions,
  getEnglishoptions,
  getOtherlanguageleveloptions,
} from "../../../http/dictionary";
export default defineComponent({
  name: "LanguageSkillsStep",
  components: {
    BaseConfirmationStep,
  },
  props: {
    data: {
      type: Object as () => LanguageSkillsData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<LanguageSkillsData>({} as LanguageSkillsData);
    const state = reactive({
      languageList: [] as any[],
      columns: [] as any[],
      englishColumns: [] as any[],
      mixedAbility: [] as any[],
    });
    // 表单验证规则
    const formRules = computed(() => ({
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          Object.assign(formData, newData);
          console.log("LanguageSkillsStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 LanguageSkillsStep handleConfirm 被调用了！");
      console.log("语言技能数据:", formData);

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSavelanguagePost,
        baseStepRef,
        emit,
        {
          onSaveSuccess: (result) => {
            console.log("语言技能保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("语言技能保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };
    //获取语言字典
    const getLanguageList = async () => {
      let res: any = await getLanguageoptions("");
      state.languageList = res.data;
    };
    //获取语言能力等级
    const getColumns = async () => {
      let res: any = await getOtherlanguageleveloptions("");
      state.columns = res.data;
    };
    //获取英语能力等级
    const getEnglishColumns = async () => {
      let res: any = await getEnglishoptions("");
      state.englishColumns = res.data;
      if (formData.languageId == 1013) {
        state.mixedAbility = state.englishColumns;
      } else {
        state.mixedAbility = state.columns;
      }
    };
    const selectlanguageName = (keywordID: number) => {
      formData.languageId = keywordID;
      if (keywordID !== 1013) {
        formData.level = 751;
        formData.levelName = "一般";
      }
    };
    onBeforeMount(async () => {
      await getLanguageList();
      await getColumns();
      await getEnglishColumns();
    });
    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      ...toRefs(state),
      selectlanguageName,
    };
  },
});
</script>
