<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 公司名称 -->
      <el-form-item label="公司名称" prop="entName">
        <el-input
          v-model="formData.entName"
          placeholder="请输入公司名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 所属行业 -->
      <el-form-item label="所属行业" prop="workIndustryId">
        <el-input
          v-model="formData.workIndustryName"
          placeholder="请选择所属行业"
          class="field-input"
          @click="getWorkIndustry"
          :readonly="true"
        />
      </el-form-item>

      <!-- 职位名称 -->
      <el-form-item label="职位名称" prop="positionTypeId">
        <el-input
          v-model="formData.positionTypeName"
          placeholder="请选择职位名称"
          class="field-input"
          @click="getPositionType"
          :readonly="true"
        />
      </el-form-item>

      <!-- 部门 -->
      <el-form-item label="所在部门" prop="department">
        <el-input
          v-model="formData.department"
          placeholder="请输入部门"
          class="field-input"
        />
      </el-form-item>

      <!-- 在职时间 -->
      <el-form-item label="在职时间" required>
        <el-row class="row-bg">
          <el-col :span="9">
            <el-form-item prop="experienceStartTime">
              <el-date-picker
                v-model="formData.experienceStartTime"
                type="month"
                placeholder="请选择"
                style="width: 100%"
                value-format="YYYY-MM"
                :disabled-date="disabledDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">至</el-col>
          <el-col :span="9">
            <el-form-item>
              <el-date-picker
                v-model="formData.experienceFinishTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                style="width: 100%"
                :disabled-date="disabledDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-checkbox
              label="至今"
              name="isToThisDay"
              v-model="formData.isToThisDay"
              class="line"
            ></el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 工作地点 -->
      <!-- <el-form-item label="工作地点">
        <el-input
          v-model="formData.workPlace"
          placeholder="请输入工作地点"
          class="field-input"
        />
      </el-form-item> -->

      <!-- 工作描述 -->
      <el-form-item label="工作描述" prop="positionDescription">
        <el-input
          v-model="formData.positionDescription"
          type="textarea"
          :rows="6"
          :maxlength="1000"
          show-word-limit
          placeholder="请描述您的工作内容和职责"
          class="field-input"
        />
      </el-form-item>
      <!-- 行业选择弹窗 -->
      <seleIndustry
        :hideValue="workIndustry"
        @confirm="confirmIndustry"
        :maxCount="1"
        v-if="dialogVisibleIndustry"
      />

      <!-- 职位选择弹窗 -->
      <seleCareer
        @confirm="confirmPosition"
        v-if="dialogVisiblePosition"
        :hideValue="positionType"
      />
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, ref } from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { WorkExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { useSaveStep } from "../composables/useSaveStep";
import seleIndustry from "@/components/seleIndustry.vue";
import seleCareer from "@/components/seleCareer.vue";

export default defineComponent({
  name: "WorkExperienceStep",
  components: {
    BaseConfirmationStep,
    seleIndustry,
    seleCareer,
  },
  props: {
    data: {
      type: Object as () => WorkExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<WorkExperienceData>({} as WorkExperienceData);

    // 弹窗状态
    const state = reactive({
      dialogVisibleIndustry: false,
      dialogVisiblePosition: false,
      workIndustry: [0, 0, 0] as number[],
      positionType: 0,
    });

    const disabledDate = (date: any) => {
      return date && date.getTime() > Date.now();
    };
    // 表单验证规则
    const formRules = computed(() => ({
      entName: [
        { required: true, message: "请输入公司名称", trigger: "blur" },
        {
          min: 1,
          max: 100,
          message: "公司名称长度在 1 到 100 个字符",
          trigger: "blur",
        },
      ],
      workIndustryId: [
        { required: true, message: "请选择所属行业", trigger: "change" },
      ],
      positionTypeId: [
        { required: true, message: "请选择职位名称", trigger: "change" },
      ],

      positionDescription: [
        { required: true, message: "请输入工作描述", trigger: "blur" },
        {
          validator: (_rule: any, value: any, callback: any) => {
            if (value && value.length > 1000) {
              callback(new Error("工作描述不能超过1000个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
    }));

    

    
    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          Object.assign(formData, newData);

          // 初始化弹窗选择值
          state.workIndustry = newData.workIndustry || [0, 0, 0];
          state.positionType = newData.positionTypeId || 0;

          console.log("🔥 初始化弹窗数据:", {
            workIndustry: state.workIndustry,
            positionType: state.positionType,
            formData: formData,
          });

          console.log("WorkExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 获取工作行业
    const getWorkIndustry = () => {
      state.dialogVisibleIndustry = true;
      console.log("🔥 设置弹窗可见:", state.dialogVisibleIndustry);
    };

    // 确认行业选择
    const confirmIndustry = (p: any) => {
      state.dialogVisibleIndustry = false;
      if (!p || !p[0]) return;

      const industry = p[0];
      formData.workIndustryName = industry.keywordName;
      formData.workIndustryId = Number(industry.keywordID);
      formData.workIndustry = [Number(industry.keywordID), 0, 0];
      state.workIndustry = [Number(industry.keywordID), 0, 0];
    };

    // 获取职位类型
    const getPositionType = () => {
      state.dialogVisiblePosition = true;
    };

    // 确认职位选择
    const confirmPosition = (p: any) => {
      state.dialogVisiblePosition = false;
      if (!p) return;

      formData.positionTypeName = p.keywordName;
      formData.positionTypeId = Number(p.keywordID);
      formData.positionType = p.keywordName;
      state.positionType = Number(p.keywordID);
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 WorkExperienceStep handleConfirm 被调用了！");

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSaveworkinfoPost, // 工作经历的保存API
        baseStepRef,
        emit,
        {
          onSaveSuccess: (result) => {
            console.log('工作经历保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('工作经历保存失败回调:', error);
          }
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      disabledDate,
      dialogVisibleIndustry: computed(() => state.dialogVisibleIndustry),
      dialogVisiblePosition: computed(() => state.dialogVisiblePosition),
      workIndustry: computed(() => state.workIndustry),
      positionType: computed(() => state.positionType),
      getWorkIndustry,
      confirmIndustry,
      getPositionType,
      confirmPosition,
    };
  },
});
</script>

<style lang="less" scoped>
// el-form 表单样式调整
:deep(.el-form-item__error) {
  position: static;
  margin-top: 4px;
}

.field-input {
  width: 100%;
}
.line{
  margin-left: 10px;
}
</style>
