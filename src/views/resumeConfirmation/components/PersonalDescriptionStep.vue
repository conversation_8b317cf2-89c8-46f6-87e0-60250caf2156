<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <el-form-item label="个人描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="6"
          :maxlength="1000"
          show-word-limit
          placeholder="请简要描述您的个人特点、优势、职业目标等"
          class="field-input"
        />
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, ref } from 'vue';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import type { PersonalDescriptionData } from '../types';
import { ResumepartClass } from '../../../http/app/Resumepart';
import { useSaveStep } from '../composables/useSaveStep';
import type { ResumeEditDesDto } from '../../../http/app/data-contracts';

export default defineComponent({
  name: 'PersonalDescriptionStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => PersonalDescriptionData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<PersonalDescriptionData>({} as PersonalDescriptionData);

    // 表单验证规则
    const formRules = computed(() => ({
      description: [
        { max: 1000, message: '描述不能超过1000个字符', trigger: 'blur' }
      ]
    }));

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        Object.assign(formData, newData);
      }
    }, { immediate: true, deep: true });

    // 数据转换函数：PersonalDescriptionData → ResumeEditDesDto
    const transformDataForAPI = (data: PersonalDescriptionData): ResumeEditDesDto => {
      return {
        desName: "个人描述", // 固定值
        desContent: data.description || "", // 描述内容
        descType: 3, // 个人描述类型
        resumeId: data.resumeId || 0, // 简历ID
        desId: data.desId || undefined // 描述ID（编辑时需要）
      };
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 PersonalDescriptionStep handleConfirm 被调用了！");

      // 创建API调用包装函数
      const apiCall = (data: ResumeEditDesDto) => {
        return ResumepartClass.apiResumepartSavedescriptionPost(
          { logtoken: "" },
          data
        );
      };

      await executeSave(
        formData,
        apiCall,
        baseStepRef,
        emit,
        {
          transformData: transformDataForAPI,
          onSaveSuccess: (result) => {
            console.log('个人描述保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('个人描述保存失败回调:', error);
          }
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit('edit', { ...formData });
      emit('confirm');
    };

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm
    };
  }
});
</script>
