<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields="{ formData: data }">
      <!-- 目前状态 -->
      <el-form-item label="目前状态" prop="workStatusId">
        <el-select
          v-model="formData.workStatusId"
          placeholder="请选择目前状态"
          class="field-input"
          :loading="loadingStatus"
          @focus="loadCurrentStatus"
          @change="changeWorkStatus"
        >
          <el-option
            v-for="option in currentStatusList"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 期望工作地 -->
      <el-form-item label="期望工作地" prop="expectWorkPlaceIds">
        <el-input
          v-model="displayWorkPlace"
          placeholder="请选择期望工作地"
          class="field-input"
          @focus="SelectCity()"
          :readonly="true"
        />
        <seleICity
          @confirmCity="confirmCity"
          v-if="dialogVisible"
          title="期望工作地"
          :maxCount="3"
          :hideValue="formData.expectWorkPlaceIds"
        />
      </el-form-item>

    
      <!-- 期望薪资 -->
      <el-form-item label="期望薪资" prop="expectSalary" required>
        <div class="salary-section salary-special">
          <el-select
            v-model="formData.expectSalary"
            placeholder="请选择期望薪资"
            class="field-input"
            :loading="loadingSalary"
            @focus="loadSalaryList"
          >
            <el-option
              v-for="option in salaryList"
              :key="option.id"
              :label="option.text"
              :value="option.id"
            />
          </el-select>
          <span class="el-input__suffix salary-unit">元/月</span>
          <el-checkbox
            v-model="formData.expectSalaryVisible"
            class="salary-checkbox"
          >
            面议
          </el-checkbox>
        </div>
      </el-form-item>

      <!-- 第一期望职位 -->
      <div v-if="showPosition1">
        <el-form-item label="期望职位" prop="expectCareer1Name" required>
          <div class="career-class">
            <el-input
              v-model="formData.expectCareer1Name"
              placeholder="请输入或选择职位"
              class="field-input"
              @focus="getExpectCareer(1)"
              :readonly="true"
            />
            <div class="field-error" v-if="test1">
              <i class="icon-warn">⚠</i>
              请您将期望职位分类选择至第三层级
            </div>
            <div class="perfect-tag" v-if="formData.isExpectCareer1">待完善</div>
          </div>
        </el-form-item>
        <el-form-item label="期望行业" prop="expectIndustry1Names" required>
          <el-input
            v-model="eIndustry1Names"
            placeholder="请选择"
            class="field-input"
            @focus="getExpectIndustry(1)"
            :readonly="true"
          />
        </el-form-item>
      </div>

      <!-- 第二期望职位 -->
      <div v-if="showPosition2">
        <el-form-item label="期望职位" prop="expectCareer2Name" >
          <div class="career-class">
            <el-input
              v-model="formData.expectCareer2Name"
              placeholder="请输入或选择职位"
              class="field-input"
              @focus="getExpectCareer(2)"
              :readonly="true"
            />
            <!-- <div class="field-error" v-if="test2">
              <i class="icon-warn">⚠</i>
              请您将期望职位分类选择至第三层级
            </div> -->
            
            <span @click="hidePosition(2)" class="btn-delete">
              <i class="icon-delete">×</i>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="期望行业" prop="expectIndustry2Names">
          <el-input
            v-model="eIndustry2Names"
            placeholder="请选择"
            class="field-input"
            @focus="getExpectIndustry(2)"
            :readonly="true"
          />
        </el-form-item>
      </div>

      <!-- 第三期望职位 -->
      <div v-if="showPosition3">
        <el-form-item label="期望职位" prop="expectCareer3Name">
          <div class="career-class">
            <el-input
              v-model="formData.expectCareer3Name"
              placeholder="请输入或选择职位"
              class="field-input"
              @focus="getExpectCareer(3)"
              :readonly="true"
            />
            <!-- <div class="field-error" v-if="test3">
              <i class="icon-warn">⚠</i>
              请您将期望职位分类选择至第三层级
            </div> -->
            
            <span @click="hidePosition(3)" class="btn-delete">
              <i class="icon-delete">×</i>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="期望行业" prop="expectIndustry3Names">
          <el-input
            v-model="eIndustry3Names"
            placeholder="请选择"
            class="field-input"
            @focus="getExpectIndustry(3)"
            :readonly="true"
          />
        </el-form-item>
      </div>

      

      <!-- 职位选择弹窗 -->
      <seleCareer
        @confirm="confirmCareer"
        v-if="dialogVisible1"
        :hideValue="expectCareer"
      />
      
      <!-- 行业选择弹窗 -->
      <seleIndustry
        :hideValue="expectIndustry"
        @confirm="confirmIndustry"
        :maxCount="3"
        :positionId="expectCareer"
        v-if="dialogVisible2"
      />
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { CareerIntentionData } from '../types';
import seleICity from "@/components/seleCity.vue";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustry from "@/components/seleIndustry.vue";
import { ResumepartClass } from '../../../http/app/Resumepart';
import { useSaveStep } from '../composables/useSaveStep';

// 本地API包装函数
const getWorkstatuoptions = async (data: any) => {
  // 使用现有的API结构
  try {
    const response = await fetch("/api/options/workstatuoptions", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return await response.json();
  } catch (error) {
    console.error("获取工作状态选项失败:", error);
    return { data: [] };
  }
};

export default defineComponent({
  name: 'CareerIntentionStep',
  components: {
    BaseConfirmationStep,
    seleICity,
    seleCareer,
    seleIndustry
  },
  props: {
    data: {
      type: Object as () => CareerIntentionData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();
    
    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();
    
    // 表单数据
    const formData = reactive<CareerIntentionData>({} as CareerIntentionData);
    
    // 表单验证规则
    const formRules = computed(() => ({
      workStatusId: [
        { required: true, message: '请选择目前状态', trigger: 'change' }
      ],
      expectWorkPlaceIds: [
        { 
          required: true, 
          validator: (rule: any, value: any, callback: any) => {
            if (!value || !Array.isArray(value) || value.length === 0) {
              callback(new Error('请选择期望工作地'));
            } else {
              callback();
            }
          }, 
          trigger: 'change' 
        }
      ],
      expectSalary: [
        { 
          required: true, 
          validator: (rule: any, value: any, callback: any) => {
            if (!value || value <= 0) {
              callback(new Error('请选择期望薪资'));
            } else {
              callback();
            }
          }, 
          trigger: 'change' 
        }
      ],
      expectCareer1Name: [
        { required: true, message: '请选择期望职位', trigger: 'change' }
      ],
      expectIndustry1Names: [
        { 
          required: true, 
          validator: (rule: any, value: any, callback: any) => {
            if (!value || !Array.isArray(value) || value.filter((name: string) => name && name.trim() !== '').length === 0) {
              callback(new Error('请选择期望行业'));
            } else {
              callback();
            }
          }, 
          trigger: 'change' 
        }
      ],
      // ...(state.showPosition2 && {
      //   expectCareer2Name: [
      //     { required: true, message: '请选择第二期望职位', trigger: 'change' }
      //   ],
      //   expectIndustry2Names: [
      //     { 
      //       required: true, 
      //       validator: (rule: any, value: any, callback: any) => {
      //         if (!value || !Array.isArray(value) || value.filter((name: string) => name && name.trim() !== '').length === 0) {
      //           callback(new Error('请选择第二期望行业'));
      //         } else {
      //           callback();
      //         }
      //       }, 
      //       trigger: 'change' 
      //     }
      //   ]
      // }),
      // ...(state.showPosition3 && {
      //   expectCareer3Name: [
      //     { required: true, message: '请选择第三期望职位', trigger: 'change' }
      //   ],
      //   expectIndustry3Names: [
      //     { 
      //       required: true, 
      //       validator: (rule: any, value: any, callback: any) => {
      //         if (!value || !Array.isArray(value) || value.filter((name: string) => name && name.trim() !== '').length === 0) {
      //           callback(new Error('请选择第三期望行业'));
      //         } else {
      //           callback();
      //         }
      //       }, 
      //       trigger: 'change' 
      //     }
      //   ]
      // })
    }));

    const state = reactive({
      currentStatusList: [] as Array<{ label: string; value: any }>,
      loadingStatus: false,
      salaryList: [] as Array<{ text: number; id: number }>,
      loadingSalary: false,
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      expectCareer: 0,
      expectIndustry: [0, 0, 0] as number[],
      sequence: 1,
      showPosition2: false,
      showPosition3: false,
      test1: false,
      test2: false,
      test3: false,
      oldCareer1: 0,
      oldCareer2: 0,
      oldCareer3: 0
    });

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        Object.assign(formData, newData);
        
        // 初始化多期望职位显示状态
        state.showPosition2 = !!(newData.expectCareer2 && newData.expectCareer2 > 0) || 
                              !!(newData.expectIndustry2 && newData.expectIndustry2[0] > 0);
        state.showPosition3 = !!(newData.expectCareer3 && newData.expectCareer3 > 0) || 
                              !!(newData.expectIndustry3 && newData.expectIndustry3[0] > 0);
        
        // 保存旧值用于校验
        state.oldCareer1 = newData.expectCareer1 || 0;
        state.oldCareer2 = newData.expectCareer2 || 0;
        state.oldCareer3 = newData.expectCareer3 || 0;
        
        console.log("CareerIntentionStep 接收到数据:", formData);
      }
    }, { immediate: true, deep: true });


    // 计算属性
    // 显示工作地的计算属性
    const displayWorkPlace = computed(() => {
      if (formData.expectWorkPlaceName && Array.isArray(formData.expectWorkPlaceName)) {
        return formData.expectWorkPlaceName.join(',');
      }
      return '';
    });

    // 显示第一期望职位
    const showPosition1 = computed(() => true);

    // 期望行业显示文本
    const eIndustry1Names = computed(() => {
      return formatIndustryNames(formData.expectIndustry1Names);
    });
    const eIndustry2Names = computed(() => {
      return formatIndustryNames(formData.expectIndustry2Names);
    });
    const eIndustry3Names = computed(() => {
      return formatIndustryNames(formData.expectIndustry3Names);
    });

    // 格式化行业名称
    const formatIndustryNames = (names?: string[]) => {
      if (!names || !Array.isArray(names)) return '';
      return names.filter(name => name && name.trim() !== '').join(',');
    };

    // 加载目前状态选项
    const loadCurrentStatus = async () => {
      if (state.currentStatusList.length > 0) return;
      
      try {
        state.loadingStatus = true;
        const res: any = await getWorkstatuoptions({});
        state.currentStatusList = res.data?.map((item: any) => ({
          label: item.keywordName,
          value: item.keywordID
        })) || [];
      } catch (error) {
        console.error('加载工作状态选项失败:', error);
        // 提供默认选项
        state.currentStatusList = [
          { label: '在职-考虑机会', value: '在职-考虑机会' },
          { label: '在职-急寻新工作', value: '在职-急寻新工作' },
          { label: '离职-正在找工作', value: '离职-正在找工作' },
          { label: '应届毕业生', value: '应届毕业生' }
        ];
      } finally {
        state.loadingStatus = false;
      }
    };

    // 加载薪资选项
    const loadSalaryList = async () => {
      if (state.salaryList.length > 0) return;
      
      try {
        state.loadingSalary = true;
        // 使用静态薪资列表数据
        state.salaryList = [
          { text: 500, id: 500 },
          { text: 1000, id: 1000 },
          { text: 1500, id: 1500 },
          { text: 2000, id: 2000 },
          { text: 2500, id: 2500 },
          { text: 3000, id: 3000 },
          { text: 3500, id: 3500 },
          { text: 4000, id: 4000 },
          { text: 4500, id: 4500 },
          { text: 5000, id: 5000 },
          { text: 5500, id: 5500 },
          { text: 6000, id: 6000 },
          { text: 6500, id: 6500 },
          { text: 7000, id: 7000 },
          { text: 7500, id: 7500 },
          { text: 8000, id: 8000 },
          { text: 8500, id: 8500 },
          { text: 9000, id: 9000 },
          { text: 9500, id: 9500 },
          { text: 10000, id: 10000 },
          { text: 15000, id: 15000 },
          { text: 20000, id: 20000 },
          { text: 25000, id: 25000 },
          { text: 30000, id: 30000 },
          { text: 35000, id: 35000 },
          { text: 40000, id: 40000 },
          { text: 45000, id: 45000 },
          { text: 50000, id: 50000 }
        ];
      } catch (error) {
        console.error('加载薪资选项失败:', error);
      } finally {
        state.loadingSalary = false;
      }
    };

    // 业务逻辑验证（期望职位分类层级）
    const validateBusinessLogic = () => {
      let errorCount = 0;
      
      if (formData.isExpectCareer1) {
        if (formData.expectCareer1 === state.oldCareer1) {
          state.test1 = true;
          errorCount++;
        } else {
          state.test1 = false;
        }
      }
      
      if (formData.isExpectCareer2) {
        if (formData.expectCareer2 === state.oldCareer2) {
          state.test2 = true;
          errorCount++;
        } else {
          state.test2 = false;
        }
      }
      
      if (formData.isExpectCareer3) {
        if (formData.expectCareer3 === state.oldCareer3) {
          state.test3 = true;
          errorCount++;
        } else {
          state.test3 = false;
        }
      }
      
      if (errorCount > 0) {
        ElMessage.error('请完善期望职位分类到第三层级');
        return false;
      }
      
      return true;
    };

    // 选择城市
    const SelectCity = () => {
      state.dialogVisible = true;
    };

    // 确认城市选择
    const confirmCity = (arr: any) => {
      state.dialogVisible = false;
      if (arr) {
        let names: any = [];
        let ids: any = [];
        arr.forEach((i: any) => {
          names.push(i.keywordName);
          ids.push(i.keywordID);
        });
        formData.expectWorkPlaceName = names;
        formData.expectWorkPlaceIds = ids;
      }
    };

    // 获取期望职位
    const getExpectCareer = (num: number) => {
      state.sequence = num;
      if (num === 1) {
        state.expectCareer = formData.expectCareer1 || 0;
      } else if (num === 2) {
        state.expectCareer = formData.expectCareer2 || 0;
      } else if (num === 3) {
        state.expectCareer = formData.expectCareer3 || 0;
      }
      state.dialogVisible1 = true;
    };

    // 获取期望行业
    const getExpectIndustry = (num: number) => {
      state.dialogVisible2 = true;
      state.sequence = num;
      if (num === 1) {
        state.expectIndustry = formData.expectIndustry1 || [0, 0, 0];
        state.expectCareer = formData.expectCareer1 || 0;
      } else if (num === 2) {
        state.expectIndustry = formData.expectIndustry2 || [0, 0, 0];
        state.expectCareer = formData.expectCareer2 || 0;
      } else if (num === 3) {
        state.expectIndustry = formData.expectIndustry3 || [0, 0, 0];
        state.expectCareer = formData.expectCareer3 || 0;
      }
    };

    // 确认职位选择
    const confirmCareer = (p: any) => {
      state.dialogVisible1 = false;
      if (p) {
        if (state.sequence === 1) {
          formData.expectCareer1Name = p.keywordName;
          formData.expectCareer1 = p.keywordID;
        } else if (state.sequence === 2) {
          formData.expectCareer2Name = p.keywordName;
          formData.expectCareer2 = p.keywordID;
        } else if (state.sequence === 3) {
          formData.expectCareer3Name = p.keywordName;
          formData.expectCareer3 = p.keywordID;
        }
      }
    };

    // 确认行业选择
    const confirmIndustry = (p: any) => {
      state.dialogVisible2 = false;
      if (!p) return;
      
      const names = p.map((i: any) => i.keywordName);
      const ids = p.map((i: any) => i.keywordID);
      
      if (state.sequence === 1) {
        formData.expectIndustry1Names = names;
        formData.expectIndustry1 = ids;
      } else if (state.sequence === 2) {
        formData.expectIndustry2Names = names;
        formData.expectIndustry2 = ids;
      } else if (state.sequence === 3) {
        formData.expectIndustry3Names = names;
        formData.expectIndustry3 = ids;
      }
    };

    // 添加期望职位
    const addIntention = () => {
      if (!state.showPosition2) {
        state.showPosition2 = true;
        return;
      }
      if (!state.showPosition3) {
        state.showPosition3 = true;
        return;
      }
    };

    // 隐藏期望职位
    const hidePosition = (num: number) => {
      if (num === 2) {
        formData.expectIndustry2Names = ["", "", ""];
        formData.expectIndustry2 = [0, 0, 0];
        formData.expectCareer2Name = "";
        formData.expectCareer2 = 0;
        state.showPosition2 = false;
      } else if (num === 3) {
        formData.expectIndustry3Names = ["", "", ""];
        formData.expectIndustry3 = [0, 0, 0];
        formData.expectCareer3Name = "";
        formData.expectCareer3 = 0;
        state.showPosition3 = false;
      }
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log('🔥 handleConfirm 被调用了！');
      const data = {...formData,salary:formData.expectSalary}
      await executeSave(
        data,
        ResumepartClass.apiResumepartSavecareernewPost,
        baseStepRef,
        emit,
        {
          validateBusinessLogic,
          onSaveSuccess: (result) => {
            console.log('保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('保存失败回调:', error);
          }
        }
      );
    };
    
    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log('BaseConfirmationStep 验证通过');
      
      // 进行业务逻辑验证（期望职位分类层级）
      if (!validateBusinessLogic()) {
        return;
      }

      // 表单验证已通过，提交数据
      emit('edit', { ...formData });
      emit('confirm');
    };

    const changeWorkStatus = (value: any) => {
      console.log(value);
      formData.workStatusId = value;
    };


    // 组件挂载时加载数据
    onMounted(async () => {
      await loadCurrentStatus();
      await loadSalaryList();
    });

    return {
      formData,
      formRules,
      currentStatusList: computed(() => state.currentStatusList),
      loadingStatus: computed(() => state.loadingStatus),
      salaryList: computed(() => state.salaryList),
      loadingSalary: computed(() => state.loadingSalary),
      dialogVisible: computed(() => state.dialogVisible),
      dialogVisible1: computed(() => state.dialogVisible1),
      dialogVisible2: computed(() => state.dialogVisible2),
      displayWorkPlace,
      showPosition1,
      showPosition2: computed(() => state.showPosition2),
      showPosition3: computed(() => state.showPosition3),
      eIndustry1Names,
      eIndustry2Names,
      eIndustry3Names,
      test1: computed(() => state.test1),
      test2: computed(() => state.test2),
      test3: computed(() => state.test3),
      expectCareer: computed(() => state.expectCareer),
      expectIndustry: computed(() => state.expectIndustry),
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      SelectCity,
      confirmCity,
      getExpectCareer,
      getExpectIndustry,
      confirmCareer,
      confirmIndustry,
      addIntention,
      hidePosition,
      loadCurrentStatus,
      loadSalaryList,
      changeWorkStatus
    };
  }
});
</script>
<style lang="less" scoped>
// @import '../styles.less';

.career-class {
  position: relative;
  
  .field-error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    
    .icon-warn {
      margin-right: 4px;
    }
  }
  
  .perfect-tag {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #ed9020;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 2px;
  }
  
  .btn-delete {
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    color: #457ccf;
    cursor: pointer;
    font-size: 18px;
    
    .icon-delete {
      font-size: 18px;
    }
  }
}

.salary-section {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .field-input {
    flex: 1;
  }
  
  // .salary-unit {
  //   color: #ddd;
  //   white-space: nowrap;
  // }
  
  .salary-checkbox {
    white-space: nowrap;
    
    :deep(.el-checkbox__label) {
      font-size: 14px;
      color: #606266;
    }
  }
}

.add-btn {
  border: 1px dashed #457ccf;
  color: #457ccf;
  
  &:hover {
    background: #f2f7ff;
  }
}


.salary-special{
  .field-input{
    flex: 0 !important;
    ::v-deep(.select-trigger){
      width: 342px !important;
    }
  }
  
}

// el-form 表单样式调整


:deep(.el-form-item__error) {
  position: static;
  margin-top: 4px;
}
</style>