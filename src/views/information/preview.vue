<template>
  <!-- //简历预览 -->
  <el-header class="header clearfix">
    <div class="ve_top_bar">
      <ul class="clearfix">
        <li class="logo clearfix">
          <Logo :bid="bid" />
          <i class="shu">|</i>
          <span class="wenzi">简历预览</span>
        </li>
        <li class="download1">
          <span class="crumbs formwork" @click="teDrawer.value = true">
            <i class="iconfont icon-formwork"></i>简历模板
            <i class="new">NEW</i>
          </span>
          <span class="crumbs" @click="prn">
            <i class="iconfont icon-download1"></i>打印
          </span>
          <span class="crumbs" @click="showPop = true">
            <i class="iconfont icon-download1"></i>下载
          </span>
        </li>
      </ul>
    </div>
  </el-header>
  <!-- 内容 -->
  <div class="content clearfix" ref="printBox" id="waterprintBox">
    <div class="Resume-preview-main clearfix">
      <div class="head-bg"></div>
      <div class="baseinfo-box">
        <div class="base-read">
          <div class="clearfix">
            <div class="sdl">
              <div class="name-bar clearfix">
                <span class="name">{{ baseInfo.name }}</span>
                <el-tag effect="plain" v-show="career.workingState">{{ career.workingState }}</el-tag>
              </div>
              <div class="info">
                <span>{{ baseInfo.age }}岁</span>
                <el-divider direction="vertical"></el-divider>
                <span>{{ baseInfo.workYear }}</span>
                <el-divider direction="vertical"></el-divider>
                <span>{{ baseInfo.talentDegreeName }}</span>
                <el-divider direction="vertical"></el-divider>
                <span>{{ baseInfo.politicalStatusName }}</span>
              </div>
              <div class="contact">
                <ul class="clearfix">
                  <li>
                    <i class="iconfont icon-shoujihao"></i>
                    {{ baseInfo.firstContact }}
                  </li>
                  <li>
                    <i class="iconfont icon-youjian"></i>
                    {{ baseInfo.email }}
                  </li>
                  <li>
                    <i class="iconfont icon-position1"></i>
                    {{ baseInfo.residencyStr }}
                  </li>
                  <li v-if="baseInfo.imContract">
                    <i class="iconfont icon-qq2"></i>
                    {{ baseInfo.imContract }}
                  </li>
                  <!-- <li v-if="baseInfo.homePage">
                    <i class="iconfont icon-home5"></i>
                    {{ baseInfo.homePage }}
                  </li> -->
                </ul>
              </div>
              <div class="advantage">
                <el-tag type="info" color="#FAFAFA" v-for="tag in form.tags" :key="tag">{{ tag }}</el-tag>
              </div>
            </div>

            <div class="sdr">
              <div class="bin-img">
                <el-image
                  style="width: 120px; height: 120px"
                  :src="url"
                  :preview-src-list="albumlist"
                  :initial-index="index"
                  fit="cover"
                ></el-image>
              </div>
              <!-- <ul class="little-img clearfix">
                <li @click="ChangeIMG(baseInfo.photo,0)">
                  <el-image :src="baseInfo.photo" style="width: 27; height: 27px" fit="cover"></el-image>
                </li>
                <li v-for="(item, index) in album" :key="index" @click="ChangeIMG(item, index+1)">
                  <el-image :src="item" style="width: 27; height: 27px" fit="cover"></el-image>
                </li>
              </ul> -->
            </div>
          </div>
        </div>
      </div>
      <div class="careerComponent-box box clearfix">
        <h1 class="title">求职意向</h1>
        <div class="list-read borderLine2">
          <ul class="clearfix">
            <li>
              <span>目前状态</span>
              <label>{{ career.workingState }}</label>
            </li>
            <li>
              <span>期望工作地</span>
              <label>{{ expectWorkPlaceName }}</label>
            </li>
            <!-- <li v-if="career.currentSalaryVisible">
              <span>目前薪资</span>
              <label v-if="career.currentSalary>0">{{ career.currentSalary }}元/月</label>
              <label v-else >未填写</label>
            </li> -->
            <li>
              <span>期望薪资</span>
              <label v-if="career.expectSalaryVisible">面议</label>
              <label v-else>{{ career.salary }}元/月</label>
            </li>
          </ul>
        </div>
        <div class="list-expect">
          <ul>
            <li class="clearfix li" v-if="career.expectCareer1">
              <h2>{{ career.expectCareer1Name }}</h2>
              <div class="common-item">
                <span>{{ career.expectIndustry1Names[0] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry1Names[1]"></el-divider>
                <span>{{ career.expectIndustry1Names[1] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry1Names[2]"></el-divider>
                <span>{{ career.expectIndustry1Names[2] }}</span>
              </div>
            </li>
            <li class="clearfix li" v-if="career.expectCareer2">
              <h2>{{ career.expectCareer2Name }}</h2>
              <div class="common-item">
                <span>{{ career.expectIndustry2Names[0] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry2Names[1]"></el-divider>
                <span>{{ career.expectIndustry2Names[1] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry2Names[2]"></el-divider>
                <span>{{ career.expectIndustry2Names[2] }}</span>
              </div>
            </li>
            <li class="clearfix li" v-if="career.expectCareer3">
              <h2>{{ career.expectCareer3Name }}</h2>
              <div class="common-item">
                <span>{{ career.expectIndustry3Names[0] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry3Names[1]"></el-divider>
                <span>{{ career.expectIndustry3Names[1] }}</span>
                <el-divider direction="vertical" v-if="career.expectIndustry3Names[2]"></el-divider>
                <span>{{ career.expectIndustry3Names[2] }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="workComponent-box box clearfix">
        <h1 class="title">工作经历</h1>
        <div class="list-work list-unify">
          <ul class>
            <li v-for="(item, index) in work" :key="index" class="common-item">
              <h1>
                {{ item.entName }}
                <i class="Small yellow" v-if="item.longestTime">最长</i>
                <i class="Small blue" v-if="item.jobSeekerAbroadExperience">海外</i>
                <label>{{ item.timeRang }}</label>
              </h1>
              <div class="info">
                <!-- <span>{{ item.enterprisePropertyName }}</span>
                <el-divider direction="vertical" v-if="item.enterprisePropertyName"></el-divider>
                <span>{{ item.enterpriseEmployeeNumberName }}</span>
                <el-divider direction="vertical" v-if="item.enterpriseEmployeeNumberName"></el-divider> -->
                <span>{{ item.workIndustryName }}</span>
                <!-- <el-divider direction="vertical" v-if="item.workIndustryName"></el-divider>
                <span>{{ item.workPlace }}</span> -->
              </div>
              <h2>{{ item.positionName }}</h2>
              <div class="info">
                <span>{{ item.department }}</span>
                <el-divider direction="vertical" v-if="item.department"></el-divider>
                <span>{{ item.workPropertyName }}</span>
                <!-- <el-divider direction="vertical" v-if="item.workPropertyName"></el-divider>
                <span>{{ item.positionLevelName }}</span> -->
              </div>
              <p class="doc" v-safe-html.relaxed="item.positionDescription"></p>
              <div class="workSkills">
              <ul><li v-for="(i, index) in item.keywordIds" :key="index">{{i.keywordName}}</li>
               </ul>
            </div>
              <div class="advanced-talent" v-if="item.higherUp">
                <div class="bar">
                  <span class="sub">汇报对象</span>
                  <label>{{ item.higherUp }}</label>
                  <span class="sub sub2">下属人数</span>
                  <label>{{ item.underlingNum }}</label>
                </div>
                <div class="bar">
                  <span class="sub">离职原因</span>
                  <label>{{ item.leavingReason }}</label>
                </div>
                <div class="bar bar-end">
                  <span class="sub">工作业绩</span>
                </div>

                <p class="doc" v-safe-html.relaxed="item.jobPerformance"></p>
              </div>
              <div class="projects" v-if="item.hasProject > 0">
                <el-collapse v-model="item.workId">
                  <el-collapse-item name="1" :title="'参与' + item.hasProject + '个项目'">
                    <div>
                      <ul class="projectsList">
                        <li v-for="(it, index) in project" :key="index">
                          <a
                            class="clearfix li"
                            v-if="it.playRole == item.workId[0]"
                            :href="'#' + it.playRole"
                          >
                            <span>{{ it.projectName }}</span>
                            <label>
                              详情
                              <i class="iconfont icon-arrowDown5"></i>
                            </label>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="educationComponent-box box">
        <h1 class="title">教育经历</h1>
        <div class="list-education list-unify">
          <ul class>
            <li v-for="(item, index) in education" :key="index" class="common-item">
              <h1>
                {{ item.school }}
                <label>{{ item.timeRang }}</label>
              </h1>
              <div class="info">
                <span>{{ item.specialityName|| item.specialityInputName}}</span>
                <el-divider direction="vertical"></el-divider>
                <span>{{ item.education }}</span>
                <el-divider direction="vertical" v-if="item.education"></el-divider>
                <span>{{ item.fullTimeFlag ? "全日制" : "非全日制" }}</span>
              </div>
              <!-- <p class="doc">{{ item.specialityDescription }}</p> -->
              <!-- 实践内容 -->
              <div class="Practice-content" v-if="item.practiceList.length > 0">
                <div class="inside" v-for="p in item.practiceList" :key="p.id">
                  <h1>
                    {{ p.practiceName }}
                    <label class="fl">{{ p.parcticeTimeSpan }}</label>
                  </h1>
                  <p class="doc" v-if="p.practiceDescription">{{ p.practiceDescription }}</p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="projectComponent-box box">
        <h1 class="title">项目经历</h1>
        <div class="list-project list-unify">
          <ul class>
            <li v-for="(p, index) in project" :key="index" class="common-item" :id="p.playRole">
              <h1>
                {{ p.projectName }}
                <label>{{ p.beginTime }} 至 {{ p.endTime }}</label>
              </h1>
              <p class="doc" v-safe-html.relaxed="p.projectDescription"></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="trainComponent-box box">
        <h1 class="title">培训经历</h1>
        <div class="list-project list-unify">
          <ul class>
            <li v-for="(p, index) in train" :key="index" class="common-item">
              <h1>
                {{ p.trainCourse }}
                <label>{{ p.trainBeginTime }} 至 {{ p.trainEndTime }}</label>
              </h1>
              <p class="tit">{{ p.trainInstitution }}</p>
              <p class="doc" v-safe-html.relaxed="p.trainingDescription"></p>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="specializedSkill-box box">
        <h1 class="title">技术能力</h1>
        <div class="otherAbilityComponent-box">
          <div class="list-project list-unify">
            <ul class>
              <li v-for="p in technology" :key="p.techId" class="common-item">
                <h1>{{ p.techName }}</h1>
                <div class="rank">
                  <el-row>
                    <el-col :span="8">
                      <span>使用时间</span>
                      <label>{{ p.monthUsed }}</label>
                    </el-col>
                    <el-col :span="8">
                      <span>掌握程度</span>
                      <label>{{ p.techLevel }}</label>
                    </el-col>
                  </el-row>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="certificateComponent-box box">
        <h1 class="title">证书职称</h1>
        <div class="list-project list-unify">
          <ul class>
            <li v-for="p in certificate" :key="p.certId" class="common-item">
              <h1>
                {{ p.certName }}
                <label>{{ p.getTime }}</label>
              </h1>
              <div class="rank">
                <span>{{ p.certTypeTitle }}</span>
                <el-divider direction="vertical"></el-divider>
                <span>{{ p.certTypeLevelName }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
       <div class="specializedSkill-box box">
        <h1 class="title">语言能力</h1>
        <div class="languageComponent-box">
          <div class="list-project list-unify">
            <ul class>
              <li v-for="p in language" :key="p.langId" class="common-item">
                <h1>{{ p.langName }}</h1>
                <div class="rank">
                  <el-row>
                    <el-col :span="8">
                      <span>综合能力</span>
                      <label>{{ p.langLevel }}</label>
                    </el-col>
                    <el-col :span="8">
                      <span>听说能力</span>
                      <label>{{ p.lsLevel }}</label>
                    </el-col>
                    <el-col :span="8">
                      <span>读写能力</span>
                      <label>{{ p.rwLevel }}</label>
                    </el-col>
                  </el-row>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="descriptionComponent-box box">
        <h1 class="title">个人介绍</h1>
        <div class="list-project list-unify">
          <ul class>
            <li v-for="p in description" :key="p.desId" class="common-item">
              <h1>{{ p.desName }}</h1>
              <p class="doc" v-safe-html.relaxed="p.desContent"></p>
            </li>
          </ul>
        </div>
      </div>
        <div class="specializedSkill-box box">
        <h1 class="title">其他技能</h1>
        <div class="languageComponent-box">
          <div class="list-project list-unify">
            <ul class>
              <li class="common-item">
                <div class="rank">
                  <el-row>
                    <el-col :span="8">
                      <span>驾&#8195;&#8194; 照</span>
                      <label>{{ otherAbility.drivingLicense }}</label>
                    </el-col>
                    <el-col :span="8">
                      <span>电脑水平</span>
                      <label>{{ otherAbility.computerLevel }}</label>
                    </el-col>
                  </el-row>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadTemplate
    v-if="showPop"
    :resumeid="resumeid"
    :resumeName="baseInfo.name"
    @ClosePop="ClosePop"
  ></DownloadTemplate>
  <MymallTemplate :drawer="teDrawer" />
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed, onMounted, nextTick, ref } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import Logo from "@/components/logo.vue";
import MymallTemplate from "@/components/MymallTemplate.vue";
import DownloadTemplate from "@/components/DownloadTemplate.vue";
import { resumeinfo, getAlbumlPhoto } from "../../http/resumeApi";
import morenPhoto from "@/assets/img/moren.png";
import { getCookies } from "@/utils/common";
import Print from '@/mixins/print.js';
export default defineComponent({
  emits: ["ReloadData"],
  components: { MymallTemplate, DownloadTemplate ,Logo},
  setup() {
    const route = useRoute();
    const state = reactive({
      resumeid: parseInt(`${route.params.id}`),
      form: {},
      baseInfo: { photo: ''},
      album: [],
      career: {},
      work: [],
      education: [],
      project: [],
      train: [],
      certificate: [],
      language: [],
      otherAbility: [],
      technology: [],
      description: [],
      url: "",
      expectWorkPlaceName: [] as any,
      index: 0,
      albumlist: [],
      showPop: false,
      type: 0,
      teDrawer: { value: false },
      bid: computed(() => {
        return parseInt(getCookies("bid"));
      }),
    });
    onMounted(() => {
      methods.getData(state.resumeid);
    });
    const methods = {
      async getData(id: number | any) {
        let data = {
          resumeid: id,
        };
        let res: any = await resumeinfo(data);
        if (res.code == 1) {
          state.form = res.data;
          state.baseInfo = res.data.baseInfo;
          state.baseInfo.photo = res.data.baseInfo.photo||(res.data.baseInfo.sex?'//image.gxrc.com/photo/head/girlnopic.gif':'//image.gxrc.com/photo/head/boynopic.gif');
          state.album = res.data.album;
          state.url = res.data.baseInfo.photo||(res.data.baseInfo.sex?'//image.gxrc.com/photo/head/girlnopic.gif':'//image.gxrc.com/photo/head/boynopic.gif');
          state.career = res.data.career;
          state.work = res.data.work;
          state.education = res.data.education;
          state.project = res.data.project;
          state.train = res.data.train;
          state.certificate = res.data.certificate;
          state.language = res.data.language;
          state.otherAbility = res.data.otherAbility;
          state.technology = res.data.technology;
          state.description = res.data.description;

          state.expectWorkPlaceName = computed(() => {
            let arr = [];
            if (res.data.career.expectWorkPlaceName[0])
              arr.push(res.data.career.expectWorkPlaceName[0]);
            if (res.data.career.expectWorkPlaceName[1])
              arr.push(res.data.career.expectWorkPlaceName[1]);
            if (res.data.career.expectWorkPlaceName[2])
              arr.push(res.data.career.expectWorkPlaceName[2]);
            return arr.join("  ");
          });
          state.work.forEach((j: any) => {
            let a = 0;
            state.project.forEach((p: any) => {
              if (p.playRole == j.workId) {
                a++;

              }
              j.hasProject = a;
            });
          });
          methods.albumList();
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获取个人形象----大图
      async albumList() {
        if (state.albumlist.length > 0) {
          return false;
        }
        let data: any = await getAlbumlPhoto("");
        if (data.code == 1 && data.data.length > 0) {
          state.albumlist = data.data.map((i: any) => {
            return i.imgUrl;
          });
          state.albumlist.unshift(state.baseInfo.photo);
        } else {
          state.albumlist = [];
        }
        
      },
    };
    const ChangeIMG = (url: string, index: number) => {
      state.index = index;
      state.url = url;
    };
    let printBox = ref(null);
    let prn = () => {
      //打印
      // state.visibility = true
      nextTick(() => {
        // window.closePrint = closePrint
        Print(false, printBox.value);
        //  state.visibility = false
      });
    };
    const fun = {
      ClosePop(num: Number) {
        if (num == 1) {
          state.showPop = false
        } else {
          state.teDrawer.value = true;
        }
      }
    }

    return {
      ...toRefs(state), ChangeIMG, methods, ...fun, prn,
      printBox,
    };
  },
});
</script>
<style lang="less" >
.Resume-preview-main {
  width: 900px;
  margin: auto;
  .head-bg {
    width: 100%;
    height: 40px;
    background: #5f9efc;
  }
  .title {
    color: #5f9efc;
    font-size: 24px;
    padding: 30px 30px 20px 30px;
  }

  .baseinfo-box {
    padding: 30px 24px 16px 24px;
    background: #fff;
    .sdl {
      float: left;
      width: 730px;
    }
    .sdr {
      float: right;
      width: 120px;
      padding: 3px 0 0 0;
      position: relative;
      height: 150px;
      .bin-img {
        width: 120px;
        height: 120px;
        position: absolute;
        right: 0;
        top: 0;
        .el-image__inner {
          width: 120px;
          height: 120px;
        }
      }
      .little-img {
        width: 130px;
        top: 123px;
        left: 0;
        position: absolute;
        li {
          width: 21px;
          height: 27px;
          float: left;
          padding: 4px 4px 0 0;
        }
      }
    }
    .name-bar {
      span {
        float: left;
      }
      span.name {
        font-size: 30px;
        color: #333;
      }
      span.el-tag {
        height: 23px;
        line-height: 23px;
        margin: 10px 0 0 15px;
        font-size: 14px;
        box-sizing: unset;
      }
    }
    .info {
      padding: 12px 0 10px 0;
      font-size: 16px;
      color: #666;
      .el-divider--vertical {
        margin: 0 15px;
      }
    }
    .contact {
      padding: 5px 0 22px 0;
      li {
        float: left;
        padding: 10px 30px 0 0;
        font-size: 16px;
        color: #666;
        i {
          padding-right: 5px;
          font-size: 20px;
        }
      }
    }
    .advantage {
      .el-tag {
        border: none;
        margin-right: 8px;
        margin-top: 5px;
        font-size: 14px;
        color: #666;
        line-height: 40px;
        height: 40px;
      }
    }
    .borderLine2::after {
      right: 0px;
      left: 0px;
    }
  }
  .careerComponent-box {
    padding: 0 0 16px 0;
    .list-read {
      line-height: 40px;
      cursor: pointer;
      margin-bottom: 16px;
      ul {
        padding: 0px 30px;

        :hover {
          box-shadow: none;
        }
      }
      li {
        width: 33%;
        float: left;
        i {
          font-style: normal;
        }
        span {
          font-size: 14px;
          color: #999;
          padding: 0 16px 0 0px;
        }
        label {
          font-size: 14px;
          color: #333;
        }
      }
    }
    .list-expect {
      .li {
        line-height: 49px;
        width: 100%;
        height: 49px;

        h2 {
          font-size: 16px;
          color: #333;
          font-weight: bold;
          float: left;
          padding: 0 24px 0 30px;
          width: 300px;
        }
        .common-item {
          span {
            font-size: 14px;
            color: #666;
            font-weight: normal;
          }
        }
        .el-divider {
          margin: 0 15px;
        }
      }
    }
    .borderLine2::after {
      bottom: -16px;
    }
  }
  .workComponent-box {
    .advanced-talent {
      padding: 24px;
      background: #fafafa;
      margin: 24px 0 0 0;
      .bar {
        padding-bottom: 20px;
        font-size: 14px;
        .sub {
          color: #999;
        }
        label {
          color: #333;
          padding-left: 16px;
        }
        .sub2 {
          padding-left: 50px;
        }
      }
      .bar-end {
        padding-bottom: 10px;
      }
    }
    .workSkills{
    ul{
      display: flex;
      flex-wrap: wrap;
    }
    li{
      background: #FAFAFA;
      color: #666666;
      font-size: 14px;
      padding: 3px 12px;
      margin-right: 5px;
      margin-top: 5px;
    }
  }
    .projects {
      padding: 20px 0 0 0;
      .el-collapse {
        border-top: none;
        border-bottom: none;
      }
      .el-collapse-item__header {
        width: 110px;
        color: #5f9efc;
        font-size: 14px;
        border: none;
      }
      .projectsList {
        width: 300px;
        height: auto;
        border-left: 4px solid #f4f5f9;
        margin: 20px 0 0 0;
        .li {
          // width: 100%;
          display: inline-block;
          padding: 0px 12px 20px;
          span {
            width: 230px;
            float: left;
            font-size: 14px;
            color: #333;
          }
          label {
            float: right;
            font-size: 13px;
            color: #999;
            cursor: pointer;
            i {
              font-size: 12px;
              color: #999;
              padding-left: 3px;
            }
          }
          &:last-child {
            padding: 0px 12px 0px;
          }
        }
      }
    }
    .list-work {
      h1 {
        label {
          float: right;
          color: #666;
        }
      }
      .common-item {
        .btn-pop {
          top: 24px;
        }
      }
    }
    .doc {
      line-height: 26px;
    }
    .Small {
      padding: 0px 5px;
      font-size: 12px;
      font-style: normal;
      font-weight: normal;
      margin-left: 12px;
    }
    .yellow {
      color: #fdaa08;
      border: 1px solid #fdaa08;
    }
    .blue {
      color: #457ccf;
      border: 1px solid #457ccf;
    }
  }
  .educationComponent-box {
    .Practice-content {
      padding: 0 24px;
      background: #fafafa;
      margin: 24px 0 0 0;
      .doc {
        padding-top: 15px;
      }
      .inside{
        padding: 24px 0 24px 0;
      }
      .fl{
        float: none;
        color: #BBBBBB;

      }
    }
    .Practice-content  div:not(:last-child){
        border-bottom: 1px solid #eee;
    }
    .list-education {
      .common-item {
        .btn-pop {
          top: 20px;
        }
      }
    }
  }
  .projectComponent-box {
    overflow: hidden;
    .doc {
      padding-top: 15px;
      line-height: 26px;
    }
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
      }
    }
  }
  .trainComponent-box {
    .doc {
      padding-top: 15px;
      line-height: 26px;
    }
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
      }
      .tit {
        font-size: 14px;
        color: #666;
        padding-top: 8px;
      }
    }
  }
  .certificateComponent-box {
    .doc {
      padding-top: 15px;
      line-height: 26px;
    }
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
      }
      .rank {
        font-size: 14px;
        color: #666;
        padding-top: 10px;
      }
    }
  }
  .languageComponent-box {
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
      }
      .rank {
        padding-top: 10px;
        span {
          font-size: 14px;
          color: #999;
        }
        label {
          font-size: 14px;
          color: #333;
          padding-left: 16px;
        }
      }
    }
    .borderLine3 {
      position: relative;
      padding: 0 30px;
    }
    .borderLine3:after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: 24px;
      top: 0;
      left: 24px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
  }
  .otherAbilityComponent-box {
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
        h1 {
          font-size: 16px;
          font-weight: normal;
        }
      }
      .rank {
        padding-top: 10px;
        span {
          font-size: 14px;
          color: #999;
        }
        label {
          font-size: 14px;
          color: #333;
          padding-left: 16px;
        }
      }
    }
  }
  .descriptionComponent-box {
    .list-project {
      .common-item {
        .btn-pop {
          top: 20px;
        }
        h1 {
          font-size: 16px;
          color: #333;
        }
      }
    }
  }
  .box {
    background: #fff;
    margin: 12px 0 0 0;
  }
  // 各模块列表统一样式
  .list-unify {
    li.common-item {
      padding: 10px 30px 30px 30px;
    }
    h1 {
      font-size: 18px;
      color: #333;
      font-weight: bold;
      label {
        font-size: 14px;
        color: #666;
        padding-left: 16px;
        font-weight: normal;
        float: right;
      }
    }
    h2 {
      font-size: 16px;
      color: #333;
      font-weight: normal;
    }
    .info {
      padding: 5px 0 15px 0;
      span {
        color: #666;
        font-size: 14px;
      }
      .el-divider {
        background-color: #f2f2f2;
        margin: 0 10px;
      }
    }
    .doc {
      color: #666;
      font-size: 14px;
      line-height: 26px;
      white-space: break-spaces;
      word-break: break-all;
    }
  }
  .el-divider--vertical {
    height: 10px;
  }
   .el-collapse-item__wrap{
    border-bottom: none;
  }
}
.header {
  width: 100%;
  background: #fff;
  height: 60px;
  line-height: 60px;
}
.ve_top_bar {
  width: 1200px;
  margin: auto;
  li {
    float: left;
  }
  .logo {
    float: left;
  }
  .logo-img {
    height: 37px;
    float: left;
    padding: 12px 0 0 0;
  }
  .shu {
    color: #f2f2f2;
    padding: 0 10px;
    font-style: normal;
  }
  .wenzi {
    font-size: 18px;
    color: #457ccf;
  }
  .nav {
    padding: 0px 20px;
    line-height: 60px;
    a {
      display: inline-flex;
      color: #333333;
      font-size: 14px;
      padding: 0 20px;
    }
    a:hover {
      color: #5f9efc;
    }
  }
  .download1 {
    float: right;
    .crumbs {
      cursor: pointer;
      font-size: 14px;
      color: #333;padding-left:20px;
      .iconfont{padding-right: 5px;}
    }
    .formwork {
      position: relative;
      padding-right: 30px;
    }
    i.new {
      position: absolute;
      top: -26px;
      right: 5px;
      font-style: normal;
      color: #fc5c5b;
      font-family: fantasy;
      font-size: 12px;
    }
  }
 
}
@page {
  size: auto A4 landscape;
  margin: 9mm;
}

@media print {
  .content {
    -webkit-print-color-adjust: exact;
    -moz-print-color-adjust: exact;
    -ms-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  button {
    display: none;
  }
}
</style>