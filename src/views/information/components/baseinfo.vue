<template>
  <div class="baseinfo">
    <div v-if="readOnly" class="base-read borderLine2">
      <div class="clearfix">
        <div class="sdl">
          <div class="name-bar clearfix">
            <span class="name">{{ passData.name }}</span>
            <el-tag effect="plain" v-show="workingState">{{ workingState }}</el-tag>
          </div>
          <div class="info">
            <span>{{ passData.age }}岁</span>
            <el-divider direction="vertical"></el-divider>
            <span>{{ passData.workYear }}</span>
            <el-divider direction="vertical"></el-divider>
            <span>{{ passData.talentDegreeName }}</span>
            <el-divider direction="vertical"></el-divider>
            <span>{{ passData.politicalStatusName }}</span>
          </div>
          <div class="contact">
            <ul class="clearfix">
              <li>
                <i class="iconfont icon-shoujihao"></i>
                {{ passData.firstContact }}
              </li>
              <li>
                <i class="iconfont icon-youjian"></i>
                {{ passData.email }}
              </li>
              <li>
                <i class="iconfont icon-icon_area"></i>
                {{ passData.residencyStr }}
              </li>
              <li v-if="passData.imContract">
                <i class="iconfont icon-qq2"></i>
                {{ passData.imContract }}
              </li>
              
            </ul>
          </div>
          <div class="advantage">
            <el-tag type="info" color="#FAFAFA" v-for="tag in tags" :key="tag">{{ tag }}</el-tag>
          </div>
        </div>
        <!-- 头像--形象照 -->
        <div class="sdr">
          <div class="bin-img">
            <el-image v-if="url" :src="url + '?t=' + Math.random()" style="width: 120px; height: 120px" fit="cover"
              :preview-src-list="albumlist" :initial-index="index"></el-image>
          </div>
          <ul class="little-img clearfix">
            <li @click="ChangeIMG(passData.photo, 0)">
              <el-image v-if="passData.photo" :src="passData.photo + '?t=' + Math.random()"
                style="width: 27; height: 27px" fit="cover"></el-image>
            </li>

            <li v-for="(item, index) in album" :key="index" @click="ChangeIMG(item, index + 1)">
              <el-image :src="item" style="width: 27; height: 27px" fit="cover"></el-image>
            </li>
          </ul>
        </div>
      </div>
      <div class="tr btn-info-edit">
        <span @click="edit()">
          <i class="iconfont icon-bianji1"></i>编辑
        </span>
      </div>
    </div>
    <!-- 编辑 -:getTags="getTags"--->
    <div v-else class="base-edit edit-unify">
      <baseinfoEdit @handleRtn="handleRtn" :resumeid="resumeid"></baseinfoEdit>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, ref, toRefs, watch, onMounted, onUpdated, nextTick, computed } from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import baseinfoEdit from "./baseinfoEdit.vue";
import { getAlbumlPhoto } from "@/http/resumeApi";
import { ElMessage } from "element-plus";
import { t } from "element-plus/lib/locale";
export default defineComponent({
  emits: ["ReloadData"],
  components: { baseinfoEdit },
  props: {
    passData: {
      type: Object,
      default() {
        return {};
      },
    },
    album: {
      type: Array,
      default: () => [],
    },
    workingState: {
      type: String,
      default: "",
    },
    tags: {
      type: Array,
      default: () => [],
    },
  },
  setup(props: any, { emit }: any) {
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      readOnly: true,
      url: props.passData.photo,
      resumeid: parseInt(`${route.params.id}`),
      index: 0,


      albumlist: computed<string[]>(() => {
        let arr: string[] = [];

        if (props.album.length > 0) {
          props.album.map((i: any) => {
            arr.push(i + '?t=' + new Date().getTime());
          });
          arr.unshift(state.url+'?t=' + new Date().getTime());
        } else {
          arr.push(state.url+'?t=' + new Date().getTime());
        }

        return arr;
      })
    });
    onMounted(() => {
      let url = window.location.href;
      if (url.indexOf("#iseditInfo") != -1) {
        state.readOnly = false;
        window.location.hash = '#baseInfo'
        store.commit("editorShow", 1);
      }
    });

    watch(
      () => props.passData.photo,
      (newValue, oldValue) => {
        state.url = props.passData.photo;
      }
    );
    const methods = {
    };
    //  获取个人信息
    const edit = () => {
      let aa = store.state.editorid;
      if (aa == 0) {
        state.readOnly = false;
        store.commit("editorShow", 1);
      } else {
        ElMessage({
          showClose: true,
          message: "请先提交打开的编辑窗口",
          type: "warning",
          duration: 1000,
        });
      }
    };
    const handleRtn = (type: number) => {

      state.readOnly = true;
      store.commit("editorShow", 0);
      // store.commit("setEditiInfo", false);
      if (type == 1) {
        emit("ReloadData");
      }
    };
    const ChangeIMG = (url: string | unknown, index: number) => {
      state.url = url;
    };

    return { ...toRefs(state), edit, handleRtn, ChangeIMG };
  },
});
</script>
<style lang="less" scoped>
.baseinfo {
  padding: 21px 24px 16px 24px;

  .sdl {
    float: left;
    width: 760px;
  }

  .sdr {
    float: right;
    width: 120px;
    padding: 3px 0 10px 0;

    .bin-img {
      width: 120px;
      height: 120px;
      background: url("@/assets/img/moren.png") no-repeat center;
      background-size: 100%;
    }

    .little-img {
      width: 124px;

      li {
        width: 27px;
        height: 27px;
        float: left;
        padding: 4px 4px 0 0;
        background: url("@/assets/img/moren.png") no-repeat center;
        background-size: 100%;
      }
    }
  }

  .name-bar {
    span {
      float: left;
    }

    span.name {
      font-size: 32px;
      color: #333;
    }

    span.el-tag {
      height: 23px;
      line-height: 23px;
      margin: 10px 0 0 15px;
      font-size: 14px;
      box-sizing: unset;
    }
  }

  .info {
    padding: 12px 0 10px 0;
    font-size: 16px;
    color: #666;

    .el-divider--vertical {
      margin: 0 15px;
    }
  }

  .contact {
    padding: 5px 0 22px 0;

    li {
      float: left;
      padding: 10px 30px 0 0;
      font-size: 16px;
      color: #666;

      i {
        padding-right: 5px;
        font-size: 20px;
      }
    }
  }

  .advantage {
    .el-tag {
      border: none;
      margin-right: 8px;
      margin-top: 5px;
      font-size: 14px;
      color: #666;
      line-height: 40px;
      height: 40px;
    }
  }

  .btn-info-edit {
    padding: 0px 0 24px 0;

    span {
      cursor: pointer;
    }
  }

  .base-edit {
    background: #fafafa;
    padding: 20px;
  }

  .borderLine2::after {
    right: 0px;
    left: 0px;
  }
}
</style>
